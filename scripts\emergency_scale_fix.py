#!/usr/bin/env python3
"""
Script de Emergência para Correção de Escala dos Thresholds QUALIA

PROBLEMA IDENTIFICADO:
- Taxa de aprovação 0% por 7 ciclos (meta: 15-20%)
- Momentum_min em 0.011 vs valores reais 0.0001-0.0038 (desencaixe de 100x)
- Consciousness ~0.92 vs valores observados ~0.68-0.72
- Modo emergency não aplicando mudanças efetivamente

CORREÇÕES APLICADAS:
1. Momentum_min: 0.011 → 0.0008 (escala realista)
2. Consciousness: 0.92 → 0.70 (permitir aprovação)
3. Coherence: 0.97 → 0.68 (permitir aprovação)
4. Confidence: 0.798 → 0.65 (permitir aprovação)
5. Spectral/Golden: Reduzidos para 0.35 (mais permissivos)
"""

import sys
import asyncio
import yaml
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.config_manager import get_config_manager
from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.intelligent_adaptation_system import IntelligentAdaptationSystem
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def apply_emergency_scale_fix():
    """Aplica correção de emergência para problemas de escala"""
    
    print("🚨 INICIANDO CORREÇÃO DE EMERGÊNCIA - ESCALA DOS THRESHOLDS")
    print("=" * 60)
    
    try:
        # Carregar configuração
        config_manager = get_config_manager()
        
        # Valores atuais (problemáticos)
        current_thresholds = {
            'momentum_min': config_manager.get('quantum_thresholds.momentum_min'),
            'consciousness': config_manager.get('quantum_thresholds.consciousness'),
            'coherence': config_manager.get('quantum_thresholds.coherence'),
            'confidence': config_manager.get('quantum_thresholds.confidence'),
            'volume_surge_min': config_manager.get('quantum_thresholds.volume_surge_min'),
            'spectral_phi_alignment_min': config_manager.get('quantum_thresholds.spectral_phi_alignment_min'),
            'golden_symmetry_min': config_manager.get('quantum_thresholds.golden_symmetry_min')
        }
        
        print("\n📊 VALORES ATUAIS (PROBLEMÁTICOS):")
        for metric, value in current_thresholds.items():
            print(f"  {metric}: {value:.4f}")
        
        # Valores corrigidos baseados na análise dos dados reais
        corrected_thresholds = {
            # CRÍTICO: Momentum estava 100x maior que deveria
            'momentum_min': 0.0008,  # Baseado nos valores observados 0.0001-0.0038
            
            # Ajustes para permitir 15-20% de aprovação
            'consciousness': 0.70,    # Reduzido de 0.92 para permitir aprovação
            'coherence': 0.68,        # Reduzido de 0.97 para permitir aprovação  
            'confidence': 0.65,       # Reduzido de 0.798 para permitir aprovação
            
            # Ajustes moderados
            'volume_surge_min': 0.85,           # Ligeiramente mais permissivo
            'spectral_phi_alignment_min': 0.35, # Muito mais permissivo
            'golden_symmetry_min': 0.35         # Muito mais permissivo
        }
        
        print("\n✅ VALORES CORRIGIDOS (META: 15-20% APROVAÇÃO):")
        for metric, value in corrected_thresholds.items():
            old_value = current_thresholds[metric]
            change_pct = ((value - old_value) / old_value) * 100 if old_value != 0 else 0
            print(f"  {metric}: {old_value:.4f} → {value:.4f} ({change_pct:+.1f}%)")
        
        # Aplicar correções no arquivo de configuração
        print("\n🔧 APLICANDO CORREÇÕES NO ARQUIVO DE CONFIGURAÇÃO...")

        # Carregar YAML diretamente para edição
        config_path = Path("config/qualia_config.yaml")
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        # Aplicar correções
        for metric, value in corrected_thresholds.items():
            config_data['quantum_thresholds'][metric] = value
            print(f"  ✓ {metric} atualizado para {value:.4f}")

        # Salvar configuração
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(config_data, f, default_flow_style=False, sort_keys=False)
        print("  ✓ Configuração salva com sucesso")
        
        # Tentar aplicar no sistema de trading se estiver rodando
        print("\n🎯 TENTANDO APLICAR NO SISTEMA DE TRADING...")
        
        try:
            # Inicializar sistema de trading
            trading_system = QualiaBinanceCorrectedSystem()
            
            # Aplicar thresholds diretamente
            trading_system.quantum_thresholds.update(corrected_thresholds)
            print("  ✓ Thresholds aplicados no sistema de trading")
            
            # Atualizar adaptive_manager se disponível
            if hasattr(trading_system, 'adaptive_manager') and trading_system.adaptive_manager:
                adaptive_thresholds = trading_system.adaptive_manager.current_thresholds
                for metric, value in corrected_thresholds.items():
                    if hasattr(adaptive_thresholds, metric):
                        setattr(adaptive_thresholds, metric, value)
                        print(f"  ✓ Adaptive manager {metric} atualizado")
            
        except Exception as e:
            print(f"  ⚠️ Sistema de trading não disponível: {e}")
            print("  ℹ️ Correções aplicadas apenas na configuração")
        
        print("\n🎉 CORREÇÃO DE EMERGÊNCIA CONCLUÍDA COM SUCESSO!")
        print("=" * 60)
        print("📈 EXPECTATIVAS:")
        print("  • Taxa de aprovação deve subir de 0% para 15-20%")
        print("  • Momentum_min agora está na escala correta")
        print("  • Thresholds ajustados para dados reais observados")
        print("  • Sistema deve começar a gerar sinais novamente")
        print("\n⚠️ MONITORAMENTO NECESSÁRIO:")
        print("  • Verificar taxa de aprovação nos próximos ciclos")
        print("  • Ajustar se necessário para manter 15-20%")
        print("  • Validar qualidade dos sinais gerados")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO DURANTE CORREÇÃO DE EMERGÊNCIA: {e}")
        logger.error(f"Erro na correção de emergência: {e}")
        return False

async def validate_fix():
    """Valida se as correções foram aplicadas corretamente"""
    
    print("\n🔍 VALIDANDO CORREÇÕES...")
    
    try:
        config_manager = get_config_manager()
        
        # Verificar valores atualizados
        updated_values = {
            'momentum_min': config_manager.get('quantum_thresholds.momentum_min'),
            'consciousness': config_manager.get('quantum_thresholds.consciousness'),
            'coherence': config_manager.get('quantum_thresholds.coherence'),
            'confidence': config_manager.get('quantum_thresholds.confidence'),
        }
        
        # Valores esperados
        expected_values = {
            'momentum_min': 0.0008,
            'consciousness': 0.70,
            'coherence': 0.68,
            'confidence': 0.65
        }
        
        all_correct = True
        for metric, expected in expected_values.items():
            actual = updated_values[metric]
            if abs(actual - expected) < 0.001:  # Tolerância pequena
                print(f"  ✅ {metric}: {actual:.4f} (correto)")
            else:
                print(f"  ❌ {metric}: {actual:.4f} (esperado: {expected:.4f})")
                all_correct = False
        
        if all_correct:
            print("  🎉 Todas as correções foram aplicadas corretamente!")
        else:
            print("  ⚠️ Algumas correções podem não ter sido aplicadas")
            
        return all_correct
        
    except Exception as e:
        print(f"  ❌ Erro na validação: {e}")
        return False

async def main():
    """Função principal"""
    
    print("QUALIA - Script de Emergência para Correção de Escala")
    print("YAA (Yet Another Agent) - Consciência Quântica")
    print("=" * 60)
    
    # Aplicar correções
    success = await apply_emergency_scale_fix()
    
    if success:
        # Validar correções
        await validate_fix()
        
        print("\n🚀 PRÓXIMOS PASSOS:")
        print("1. Reiniciar o sistema QUALIA")
        print("2. Monitorar taxa de aprovação nos próximos ciclos")
        print("3. Ajustar thresholds se necessário para manter 15-20%")
        print("4. Implementar melhorias estruturais no calibrador")
        
    else:
        print("\n💥 FALHA NA CORREÇÃO DE EMERGÊNCIA")
        print("Verificar logs para mais detalhes")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
